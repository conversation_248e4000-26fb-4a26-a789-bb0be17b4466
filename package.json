{"name": "klook-agent-web", "version": "0.1.0", "private": true, "scripts": {"dev": "APP_ENV=development KL_ARGS_ENV=dev NODE_ENV=development npx nodemon --harmony ./bin/dev.js", "dev:cn": "APP_ENV=development KL_ARGS_ENV=aliyunfat KL_ARGS_APP=aliyunfat KL_ARGS_SITE=cn NODE_ENV=development npx nodemon --harmony ./bin/dev.js", "start": "node ./server/index.js", "start:test": "NODE_ENV=test  node ./server/index.js", "build:testing": "NODE_ENV=test vue-cli-service build", "build:staging": "DEPLOY_ENV=staging vue-cli-service build", "build": "vue-cli-service build", "build:prod": "vue-cli-service build", "lint": "eslint --ext .json,.js,.vue --ignore-path .gitignore .", "format": "prettier --write  \"src/**/*.js\" \"src/**/*.vue\"", "test:unit": "vue-cli-service test:unit", "build:amp-sdk": "webpack --config ./sdk/webpack.config.js"}, "lint-staged": {"*.{js,vue}": ["prettier --write", "eslint --fix", "git add"]}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "dependencies": {"@klook/aws-encryption": "0.5.1", "@klook/captcha": "2.0.0", "@klook/client-report": "^0.3.3", "@klook/env-utils": "^0.0.2", "@klook/klk-kcrypto": "^0.0.2", "@klook/klook-experience-itinerary-v2": "0.1.4", "@klook/empty-panel": "^1.1.7", "@klook/galileo-vue": "1.3.2", "vue-awesome-swiper": "3.1.3", "vue-lazyload": "1.3.3", "swiper": "5.3.1", "@klook/map": "1.2.0", "@klook/klook-scroll-snap": "0.0.2", "@klook/klook-ui": "1.38.14", "@klook/kms-client": "^0.0.9", "@klook/kv": "^1.0.16", "@klook/logquery": "4.9.1", "@klook/otel": "1.0.22", "@klook/standalone-checkout-helper": "0.1.8", "@klook/platform-countdown": "0.0.5", "@klook/klook-icons": "0.20.0", "async-validator": "^1.8.2", "axios": "^0.18.0", "base-58": "0.0.1", "date-fns": "^1.29.0", "debug": "^3.1.0", "js-cookie": "^2.2.0", "js-sha1": "^0.6.0", "koa": "^2.5.1", "koa-bodyparser": "^4.2.1", "koa-logger": "^3.2.0", "koa-onerror": "^4.0.0", "koa-router": "^7.4.0", "koa-send": "^4.1.3", "lodash": "^4.17.10", "marked": "^0.4.0", "qiankun": "^2.5.1", "request": "^2.87.0", "request-promise-native": "^1.0.5", "vue": "^2.5.16", "vue-click-outside": "^1.1.0", "vue-i18n": "^7.8.0", "vue-router": "^3.5.2", "vuex": "^3.1.0", "web-vitals": "^1.1.0"}, "devDependencies": {"@babel/plugin-proposal-nullish-coalescing-operator": "^7.18.6", "@babel/plugin-proposal-optional-chaining": "^7.21.0", "@vue/cli-plugin-babel": "^3.0.0-beta.15", "@vue/cli-plugin-eslint": "^3.0.0-beta.15", "@vue/cli-plugin-unit-jest": "^3.0.0-beta.15", "@vue/cli-service": "^3.0.0-beta.15", "@vue/eslint-config-standard": "^3.0.1", "@vue/eslint-config-typescript": "^3.1.0", "@vue/test-utils": "^1.0.0-beta.29", "babel-core": "7.0.0-bridge.0", "babel-jest": "^23.0.1", "babel-plugin-date-fns": "^0.2.1", "babel-plugin-import": "^1.13.0", "babel-plugin-transform-commonjs-es2015-modules": "^4.0.1", "babel-preset-env": "^1.7.0", "compression-webpack-plugin": "^1.1.11", "eslint-config-airbnb-base": "^13.1.0", "eslint-import-resolver-alias": "^1.1.2", "eslint-plugin-import": "^2.17.2", "eslint-plugin-prettier": "^3.0.1", "html-webpack-harddisk-plugin": "^0.2.0", "husky": "^2.7.0", "jest": "^24.5.0", "lint-staged": "^8.1.5", "node-sass": "^6.0.0", "prettier": "^1.16.4", "sass-loader": "10.2.0", "svg-sprite-loader": "^4.1.3", "vue-jest": "^3.0.4", "vue-template-compiler": "^2.5.16", "webpack-bundle-analyzer": "^2.13.1", "webpack-cli": "^3.3.6"}, "overrides": {"nats": "2.10.2"}, "browserslist": ["> 1%", "last 2 versions", "not ie <= 8"]}