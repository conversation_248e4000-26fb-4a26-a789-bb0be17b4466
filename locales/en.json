{"global": {"currency": {"CNY": "Chinese Yuan", "HKD": "Hong Kong Dollar", "SGD": "Singapore Dollar", "TWD": "New Taiwan Dollar", "USD": "U.S. Dollar", "JPY": "Japanese Yen", "KRW": "Korean Won", "MYR": "Malaysian Ringgit", "IDR": "Indonesian Rupiah", "AUD": "Australian Dollar", "INR": "Indian Rupee", "NZD": "New Zealand Dollar", "THB": "Thai Baht", "VND": "Vietnamese Dong", "EUR": "Euro", "GBP": "British Pound"}, "en-BS": "English (International)", "zh-CN": "中文（简体）", "zh-TW": "中文（繁體）", "ko": "한국어", "en-AU": "English (Australia)", "en-CA": "English (Canada)", "en-HK": "English (Hong Kong, SAR)", "en-IN": "English (India)", "en-MY": "English (Malaysia)", "en-NZ": "English (New Zealand)", "en-PH": "English (Philippines)", "en-SG": "English (Singapore)", "en": "English", "en-GB": "English (UK)", "ja": "日本語", "th": "ไทย", "vi": "Tiếng <PERSON>", "id": "Bahasa Indonesia", "fr": "Français", "es": "Español", "de": "De<PERSON>ch", "it": "Italiano", "ru": "Русский"}, "manual": "The Manual", "citi_bank_hongkong": "Citibank N.A., Hong Kong Branch", "singapore_dbs_bank": "Citibank N.A., Singapore Branch", "citi_bank_sydney": "Citibank, N.A. (Sydney Branch)", "citi_bank_bangkok": "Citibank N.A.Bangkok Branch", "citi_bank_mumbai": "Citibank N.A., Mumbai Branch", "citi_bank_hanoi": "Citibank N.A. - Hanoi Branch", "jpmorgan_bank_amsterdam": "JPMorgan Chase Bank, N.A. Amsterdam", "jpmorgan_bank_london": "JPMorgan Chase Bank, N.A. London", "citi_bank_indonesia": "Citibank, N.A. Indonesia", "ctbc": "CTBC Bank", "taiwan_acount_name": "Klook Travel Taiwan Limited", "citi_bank": "Citibank", "argicultural_bank": "Agricultural Bank of China (ABC)", "argicultural_bank_detail": "中国农业银行股份有限公司深圳天安支行", "global_validate_lettersonly": "Please enter English characters", "global_validate_letter": "Please enter", "cannot_be_empty": "required", "global_validate_plzEnterValidEmail": "Please enter a valid email", "email_already_exists": "Email already exists", "global_password_validate": "8-20 characters long, letters and numbers contained", "global_password_not_match": "Password does not match", "confirm": "Confirm", "cancel": "Cancel", "no_results_find": "Sorry, no data can be found.", "page_404_message": "Whoops!", "page_404_tips": "404 Error - the requested page does not exist.<br/>You'll go back to the homepage in <span class='count'>{0}</span> seconds (or click <a class='t_main' href='/'>here</a>)", "settings": "Settings", "change_password": "Change Password", "current_password": "Current Password", "new_password": "New Password", "confirm_password": "Confirm Password", "save": "Save", "submit": "Submit", "success": "Success", "old_password_incorrect": "Old password incorrect", "activity.v2.label.summary.bookding_details": "Booking Details", "activity.v2.label.summary.date": "Date", "activity.v2.label.summary.time": "Time", "activity.v2.btn.summary.add_to_cart": "ADD TO CART", "activity.v2.label.package_options": "Package Options", "activity.v2.label.activity_desc": "What To Expect", "activity.v2.label.activity_info": "Activity Information", "activity.v2.label.how_to_use": "How To Use", "activity.v2.label.reserve_policy": "Terms & Conditions", "activity.v2.label.tnc": "Cancelation Policy", "activity.v2.usage.img": "View photo instructions", "activity_detail_not_exist": "This activity does not exist.", "global.text.index": "Home", "activity.v2.btn.sold_out": "Sold Out", "activity.v2.btn.book_now": "BOOK", "activity.v2.label.instant_confirmation": "Instant Confirmation", "activity.v2.label.activity_unavaiable": "Currently Unavailable", "activity.v2.label.today_available": "Available Today", "book.tomorrow": "Available Tomorrow", "activity.v2.label.earliest_available": "Earliest available date:", "activity.v2.button.check_availability": "Check Availability", "activity.label.select_date": "Select Date", "activity.tips.please_select_date": "Please select date", "activity.label.select_time": "Select Time", "activity.v2.label.select_quantity": "Package Quantity", "activity.v2.label_more_info": "Package Details", "activity.v2.btn.select_package1": "SELECTED", "activity.v2.btn.select_package": "SELECT", "activity.v2.btn.select_package2": "RESELECT DATE", "datepicker.date.soldout": "Sold Out", "activity.tips.please_select_unit": "Please select", "must_buy": "Minimum requirement", "desktop.index.search_placeholder": "Search by destination, activity or attraction", "recently_booked": "RECENTLY BOOKED", "theme.see_more": "See More", "you_have_not_collected_any_activity": "You haven't added any activity to Favorites yet.", "log_out": "Log Out", "for_enquiry": "For Enquiry", "feedback": "<PERSON><PERSON><PERSON>", "api_list": "API List", "destination": "Destination", "country_or_regional": "Country Or Regional", "city": "City", "template": "Template", "all": "All", "activity_title_id": "Activity Title/Activity ID", "search": "Search", "activities_list": "Activity List", "export": "Export", "see_package_list": "See Package List", "package_list": "Package List", "activity_title": "Activity Title", "currency": "<PERSON><PERSON><PERSON><PERSON>", "activity_id": "Activity ID", "package_name": "Package Name", "unit": "Unit", "sku_id": "SKU ID", "retail_price": "Retail Price", "agent_rate": "Agent Rate", "attractions_and_shows": "Attractions & Shows", "tours_and_sightseeing": "Tours & Sightseeing", "activities_and_experiences": "Activities & Experiences", "food_and_wellness": "Food & Wellness", "transport_and_wifi": "Transport & WIFI", "bookings": "Bookings", "date_range": "Date Range", "activity_name": "Activity Name", "traveler": "Traveler", "to_pay": "去支付", "voucher_order": "Voucher-Split Orders", "participation_date": "Participation Date", "filter_by_booking_agent": "Booking Accounts", "booking_order_no": "Order No / Booking No", "booking_batch_order_no": "Order No", "agent_booking_ref_num": "Agent Booking Ref. No.", "edit": "Edit", "operation": "Operation", "download_voucher": "Download", "booking_records": "Booking Records", "all_orders": "All orders", "total_amount": "Total Amount", "settlement_price": "Settlement Price", "see_more": "See more", "order_number": "Order Number", "booking_date": "Booking Date", "traveler_name": "Traveler Name", "email_address": "Email Address", "phone_number": "Phone Number", "phone_number_validate": "Can only input number here, 6-20 digits long.", "order_phone_number_validate": "Can only input number here, 6-17 digits long.", "amount_paid": "Amount <PERSON>", "export_search_result": "Export Search Result", "export_all_orders": "Export All Orders", "export_progress_tip": "Booking Records Processing", "booking_account": "Booking Account", "order_no": "Order No.", "amount": "Amount", "details": "Details", "units": "Units", "expiry_date": "Expiry Date", "order_status": "Order Status", "activity_and_package": "Activity & Package", "booking_no": "Booking No.", "extra_information": "Extra Information", "status": "Status", "subtotal": "Subtotal", "refunded": "Refund or not", "refunded_amount": "Refund Amount", "refunded_time": "Refund Time", "today": "Today", "this_month": "This month", "last_1_month": "Last 1 month", "last_3_month": "Last 3 months", "last_1_year": "Last 1 year", "city.category.all_activity": "Browse All", "city.search.place_holder": "Search activities", "header.WishList": "Favorites", "balance": "Balance", "available_balance": "Available Balance", "credit_limit": "Credit Limit", "settlement_due": "Settlement Due", "topup": "Top Up", "topup_amount": "Top Up Amount", "transfer_receipt": "Transfer Receipt ", "transfer_receipt_tip": "Please take a photo or screenshot of the transfer receipt and upload here", "upload": "Upload", "upload_image_tip1": "JPG or PNG file (less than 200k)", "bank_info_tip1": "Account top up working hours: 10:00am-7:00pm Monday to Friday.", "bank_info_tip2": "Your account will be topped up by the actual amount we receive.Please do not submit the same request twice as this may delay the top up process.", "bank_info_tip3": "Please submit your top up request in advance to allow the bank to process the transfer.", "date": "Date", "previous_balance": "Previous Balance", "topup_type": "Top Up Type", "review_status": "Review status", "user_topup": "User Topup", "is_reviewing": "Is Reviewing", "approved": "Approved", "rejected": "Rejected", "upload_success": "Upload Success", "topup_success": "Topup Success", "global_email_link_expired_header": "This link has expired.", "global.forgetpwd": "Reset Password", "input_find_password_tip": "Enter the email address that you used to create your account and we will send you a link to reset your password.", "global.hassend.tips": "An email has been sent to your inbox. Just follow the instructions to replace your password.", "my_account": "My Account", "title": "Title", "administrator_registration": "Administrator Registration", "email": "Email", "signup_password": "Password", "signup_confirm_password": "Confirm Password", "next_step": "Next Step", "signup_step1_tip": "The email address is also your login. You need your email address and password to log in to your {0} account.", "signup_step3_success_text": "Your registration application has been sent to our account manager at {0} on {1}. We will process your application within 3 business days and will inform you as soon as possible by mail.", "signup_step3_contact_text": "If you have any questions, please contact us at: {0}", "registration_email_address": "Registration Email Address: {0}", "first_name": "First Name", "last_name": "Last Name", "company_name": "Company Name", "signup_email_placeholder": "Please enter your email", "signup_pwd_placeholder": "Please enter your password", "signup_confirm_pwd_placeholder": "Please confirm your password", "country_or_regional_code": "Country Or Regional Code", "language": "Language", "address": "Address", "website_url": "Website URL (include http://)", "website_name": "Website Name", "login.subtitle": "5 reasons to partner with us", "login.intro1": "Access a Wild Selection of Quality Activities in the world", "login.intro2": "Most Competitive Rates", "login.intro3": "Tailor-Made Solutions", "login.intro4": "Best-In-Class Technology", "login.intro5": "Partner Support", "login.login.header": "LOG IN", "login.email": "Email Address", "login.password": "Enter Password", "login.rememberme": "Remember Me", "login.forget": "Forgot your password?", "login.login": "LOG IN", "login.signup": "SIGN UP", "login.partner": "Partnerships", "login.partner.intro": "We are proud to work with over 10,000 of the world's leading brands and attractions", "login.email.error1": "Please enter a valid email", "login.password.error2": "Password must be at least 8 characters", "login.password.error1": "Please enter password", "reset.password": "RESET PASSWORD", "reset.password.error1": "Password must be at least 8 characters", "reset.newpassword": "New Password", "reset.placeholder.new": "Please enter your new password", "reset.confrimpassword": "Confirm Password", "reset.confirm": "Confirm", "agent_sign_up": "AGENT SIGN UP", "signup.label.have_account_and_login": "Already have an account?", "our_agreement": "Our Agreement", "signup_agreement_label": "Please read our Travel Agent Agreement", "agree_term": "Agree to Travel Agent Agreement", "registration_has_been_sent_successful": "Registration application has been sent successful", "sub_accounts": "Sub Accounts", "add_sub_account": "Add Sub Account", "locked": "Locked", "normal": "Normal", "lock_account": "Lock Account", "unlock_account": "Unlock Account", "reset_password": "Reset Password", "allow_balance_access": "Allow Balance Access", "remove_balance_access": "Remove Balance Access", "view_bookings": "View Bookings", "password": "Password", "global.select.palceholder": "Please Select", "other_country_or_regional": "Other Country Or Regional", "yes": "Yes", "no": "No", "country_code": "Country Code", "account_status": "Account Status", "modify": "Modify", "whether_lock_sb_account": "Are you sure you want to lock this sub account?", "whether_unlock_sb_account": "Are you sure you want to unlock this sub account?", "whether_allow_sb_access_to_balance": "Are you sure you want to allow {0} access to account balances?", "whether_remove_sb_access_to_balance": "Are you sure you want to remove {0} access to account balances?", "account_number": "Account Number", "account": "Account", "bank_name": "Bank Name", "account_name": "Account Name", "iban": "IBAN", "bank_code": "Bank Code", "swift_code": "Swift Code", "usd_account_number": "Account Number（USD)", "twd_account_number": "Account Number（TWD)", "sgd_account_number": "Account Number（SGD)", "krw_account_number": "Account Number（KRW)", "hkd_account_number": "Account Number（HKD)", "jpy_account_number": "Account Number（JPY)", "cny_account_number": "Account Number（RMB)", "myr_account_number": "Account Number（MYR)", "inr_account_number": "Account Number（HKD)", "nzd_account_number": "Account Number（NZD)", "thb_account_number": "Account Number（THB)", "vnd_account_number": "Account Number（HKD)", "aud_account_number": "Account Number（AUD)", "eur_account_number": "Account Number（EUR)", "gbp_account_number": "Account Number（GBP)", "idr_account_number": "Account Number（IDR)", "bank_address": "Bank Address", "shoppingcart": "Shopping Cart", "activity.v2.btn.publish": "Activity Unavailable", "promo_code.coupon.expired": "Expired", "shoppingcart.paynow": "Pay Now", "shoppingcart.table.activity.desc": "Activity Description", "global.date": "Date", "shoppingcart.table.units": "Units", "global.opt.delete": "Delete", "my_balance": "My Balance", "my_bookings": "My Bookings", "monthly": "Monthly", "my_bookings_monthly_desc_pop_content": "It counts real-time data on the first day of every month until you open this page and will be cleard at 23:59 on the last day of the month.", "yesterday_amount": "Yesterday Amounts", "yesterday_bookings": "Yesterday Bookings", "total": "Total", "my_bookings_total_desc_pop_content": "It counts real-time data from the day your registration was approved until the moment you open this page and will not be cleared.", "last_seven_days_total_amount": "Last 7 Days Amounts", "last_seven_days_total_bookings": "Last 7 Days Bookings", "global.nav.shopping_cart": "<PERSON><PERSON>", "voucher_details": "Voucher Details", "jv_agent_title": "Agent", "login.ok": "OK", "global.standard.date.format": "D MMM YYYY", "login.checking.header": "Registration application has been sent successful", "login.rejected.header": "Sorry, your registration application did not pass", "business_detail": "Business Details", "signup_company_selling_label": "What products are you currently best selling? ", "signup_top_destination_label": "What destination are you currently focus on? ", "monthly_sales": "Monthly sales (optional)", "monthly_independent_visits_label": "Monthly Independent visit (optional)", "know_way_label": "How did you know about us?", "social_media": "Social Media", "news": "News", "traveler_exhibition": "Traveler Exhibition", "app_store": "App Store", "friends": "Friends", "other_partner": "Other Partner", "offline_promotion": "Offline Promotion", "others": "Others", "applicant_information": "Applicant Information", "company_details": "Company Details", "company_phone_number": "Company's Phone Number", "company_email_address": "Company's Email Address", "number_of_employees": "Number of Employees", "date_of_establishment": "Date of Establishment", "travel_agency_insurance_policy": "Travel Agency Insurance Policy", "applicant_business_card": "Applicant's Business Card", "information_about_the_company": "Information about the company", "business_license": "Business License", "click_upload": "Click to Upload", "delete": "Delete", "travel_agency_license": "Travel Agency License", "signup_choose_cooperation_type_tip": "Choose the type of coopration you want (multiple choice)", "signup_cooperation_platform": "Agent Marketplace", "signup_cooperation_api": "Agent API", "top.search.destination": "Search Destinations", "travel_agent_details": "Travel Agent Details", "signup_currency_input": "Preferred Currency", "signup_currency_input_tip": "Once the currency has been confirmed, it can not be edited.", "signup_travel_agency_tip": "If you are a travel agency, please also provide Travel Agency License and Travel Agency Insurance Policy.", "signup_company_information_tip": "e.g. Brochure / leaflet", "job_description": "Job Title", "voucher_info_desc": "Filling in this information is optional. If you fill in any part of this page, that information will appear in the customer's voucher email.", "click_view_voucher_email_template": " View sample voucher email", "upload_company_logo": "Upload Your Company Logo", "optional": "Optional", "select_file": "Select File", "upload_company_logo_tip1": "By uploading your company logo you will replace the default logo on the top of the voucher email. ", "upload_company_logo_tip2": "Your file should be size 160x40 px (no more than 200KB) in JPG, PNG or JPEG format.", "contact_number": "Contact Number", "company_address": "Company Address", "submit_success": "Submit Success", "submit_failed": "Submit Failed", "activity.v2.earliest_available": "Book now for: {0}", "activity.v2.error.package_min_pax": "You must select at least <span class='min_pax'>{0}</span> for this package", "activity.error.price_min": "You must select at least {0} unit{1}", "activity.error.price_max_v2": "You can only select up to {0} unit(s)", "activity.error.package_min": "You must select {0} or more for this package", "activity.error.package_max": "You can not select more than {0} for this package", "jv_name_topup": "{0} Topup", "topup_review_tip": "Your top up application has been submitted to {1} on {0} and will be confirmed within 2 working days", "topup_review_tip_passed": "Your top up application has been approved at {0}", "topup_review_tip_failed": "Your top up application has been rejected at {0}", "confirm_read_t_c": "I have read and agreed to the <a class='agreement_text' target='_blank' href='{0}'>Terms & Conditions</a>", "var_account_number": "{0} Account Number", "login.checking.content1": "Your registration application will be sent to our account manager at {0} on {1}. We will process your application within 3 business days and will inform you as soon as possible by mail.", "login.checking.content2": "If you have any questions, please contact us at: {0}", "login.rejected.content": "If you have any questions, please contact us at: {0}", "please_input_integer_bigger_than_zero": "Input is not valid, please enter a number greater than 0!", "please_input_english_characters": "Please enter English characters", "please_input_valid_phone_number": "Please input valid phone number", "family_name": "Family Name", "mobile_number": "Mobile Number", "voucher_language": "Voucher Language", "agent_booking_ref_no": "Agent Booking Ref.No (Optional)", "travel_agent_info": "Travel Agent Information", "traveler_info": "Traveler's Information", "payment_type": "Payment Type", "pay_security_tip": "All card information is fully encrypted, secure, and protected.", "credit_card": "Credit Card", "credit_balance": "Credit Balance", "paypal": "<PERSON><PERSON>", "alipay": "Alipay", "t_and_c": "Terms & Conditions", "pay_btn": "Pay Now", "credit_card_number": "Card Number", "credit_card_expiry_date": "Expiry Date", "credit_card_cvc": "Security Code", "credit_card_save": " Save card details", "see_more_other_type": "See more payment type", "saved_cards": "Saved Cards", "payment_processing_tip": "Payment processing, please wait...", "add_newcard": "Add New Card", "payment_failed": "Payment Failed", "extra_info": "Extra Details", "payment_amount": "Payment Amount", "total_saving": "Total Saving", "pay_complete": "Payment Complete!", "pay_failed": "Payment Failed", "pay_required_text_field": "Required field", "pay_required_select_field": "Please select", "pay_traveler": "Traveler", "pay_security_tip1": "Your details are safe with us. All data is encrypted and transmitted securely with an SSL protocol.", "pay_security_tip2": "", "pay_privacy_statement": "Privacy Statement", "pay_data_security": "Data Security", "pay_total_tip": "Because there may be differences in handling fees between different payment methods, the actual amount payable is subject to the data displayed when submitting the payment.", "search_related_destination": "Related Destinations", "search_related_destination_holder": "Select one or more Destinations", "search_related_categories_holder": "Select one or more Categories", "search_related_categories": "Related Categories", "search_instant_confirm": "Instant Confirm", "search_date_today": "Today", "search_date_tomorrow": "Tomorrow", "search_date_all_available": "All Available Dates", "search_sort_by": "Sort By", "search_sort_best_match": "Best Match", "search_sort_most_popular": "Most Popular", "search_result_find": "{0} Experiences Matched for {1}", "search_no_result": "Sorry, no results were found for '{0}'", "search_go_home": "Go Back To Homepage", "search_filter_no_result": "No results found, please reset your filters", "search_reset_filter": "Reset All Filters", "search_reset": "Reset", "pay_cvv_invalid": "CVV code is invalid. Please double confirm your input.", "contact_account_manager": "Contact Account Manager", "trending_now": "TRENDING NOW", "account_inactive_tip": "Your account will be disable after 72 days of inactivity.", "help_tip": "If you need help, you can contact your account manager for assistance.", "no_recented_buy": "You have not made any bookings yet.", "activity_in_city": "Search activities in {0}", "edited_selections": "Edited Selections", "bulk_buy": "Bulk Buy", "bulk_but_subTitle": "We offer the bulk buy service (more than 100 tickets at one time), please contact <span class='email_contact'>{0}</span> if needed", "input_activity_id": "Please input activity ID", "activity_id_error": "Wrong Activity ID", "find": "Search", "submit_order": "Submit", "ordinary_order": "Ordinary Order", "bulk_order": "Bulk Buy", "available_date": "Select the available date", "pending_payment": "Pending Payment", "pending_confirm": "Pending Confirm", "pending_ticketing": "Pending Ticketing", "complete": "Completed", "canceled": "Canceled", "search_result": "Search Result", "handling_fee": "Handling Fee", "handling_fee_tips_credit": "In the event of a refund, the credit card payment will not be refunded.", "handling_fee_tips_paypal": "In the event of a refund, the PayPal payment will not be refunded.", "bulk_buy_visible": "Bulk Buy Visible", "bulk_buy_inVisible": "Bulk Buy Invisible", "bulk_buy_tips": "Your order has been submitted. Please check on the bookings page.", "topup_note": "Note", "topup_channel": "Top Up Channel", "channel_cannot_be_empty": "Please choose a topup channel", "favorites": "Favorites", "citi_bank_berhad": "Citibank Berhad", "activity_tag_hot": "HOT", "activity_tag_new": "NEW", "activity_tag_exclusive": "EXCLUSIVE", "activity_tag_promo": "PROMO", "banking_corporation": "The Hongkong and Shanghai Banking Corporation", "agent_input_limit": "Enter an integer from {0} to {1}", "quantity": "Quantity", "no_match_tip": "No data matching", "wechat_pay": "Wechat Pay", "payment_account": "Payment Account", "payment_qrcode_valid": "QR Code Validity", "payment_qrcode_expired": "QR code has expired, please re-order", "payment_wechat_scan": "Please open WeChat and scan the QR code below to pay", "payment_wechat_pay_tips": "Not being redirected to the Klook payment page?", "payment_wechat_pay_tips_info": "If you are not redirected after completing your WeChat payment, please go to the Booking page to check the status of your order", "check_details": "Check Details", "balance_details": "Balance Details", "agent_register_account": "Agent Register Account", "credit_limit_amount": "Credit Limit Amount", "available_top_up_amount": "Available Top Up Amount", "last_top_up_amount": "Last Top Up Amount", "last_approved_date": "Last Approved Date", "last_top_up_type": "Last Top Up Type", "fixed_deposit_amount": "Fixed Deposit Amount", "topup_review_tip_delete_success": "Successfully Deleted", "available_credit_limit_amount": "Available Credit Limit Amount", "actual_date": "Actual Date", "flash_sale": "FLASH SALE", "day": "Days", "hour": "Hours", "minute": "<PERSON>s", "second": "Sec", "end_in": "End in", "promotion_finished_data": "The Flash Sale has ended and the normal price has been restored", "global.warning": "Warning", "promotion_expired_tip": "Some promotion activity  has ended，The price changed from {0} to {1},  continue to submit? ", "promotion_expired_tip_back": "Recheck", "promotion_expired_tip_continue": "Submit", "please_enter_search": "Please enter", "refund_of_not": "Refund or not", "activity_info": "Activity Info", "more_filters": "More Filters", "less_filters": "Less Filters", "booking_list": "Booking list", "detail_page": "Detail Page", "order_info": "Order Info", "order_channel": "Order Channel", "payment_date": "Payment Date", "booking_agent_name": "Booking Agent Name", "booking_agent_mobile": "Booking Agent Mobile", "booking_agent_email": "Booking Agent <PERSON><PERSON>", "promo_code": "Promo Code", "view_detail": "View Detail", "booking_details": "Booking Details", "back_to_booking_list": "Back to Booking List", "actions": "Actions", "current_bookings": "Current Booking(s)", "payment_amount_and_type": "Payment Amount & Type", "start_date": "Start Date", "end_date": "End Date", "country_or_regions": "Country or Regions", "traveler_mobile": "Traveler Mobile", "traveler_email": "Traveler <PERSON><PERSON>", "show_less": "Show Less", "show_more": "Show More", "please_input_agent_booking_ref_num": "Please Input Agent Booking Ref. No.", "booking_status": "Booking Status", "not_confirm": "Unconfirmed", "is_confirmed": "Confirmed", "is_canceled": "Cancelled", "is_refunded": "Refunded", "view_voucher": "View Voucher", "booking_channel_amp": "AMP", "booking_channel_agent_api": "AgentAPI", "booking_channel_batch": "<PERSON><PERSON>", "paid": "Paid", "loading": "Loading...", "global.nav.message": "Notification", "message.markAllAsRead": "<PERSON>", "message.nav.all": "All Notification", "message.nav.balance": "Balance Notice", "message.nav.activity": "Activity Change", "message.nav.system": "System Notification", "message.noNotification": "There is no notification yet.", "collection.tips": "When activities’s information in your favorites list has been updated, you will receive a notification.", "children_pay_is_not_allowed.tips": "The main account can view sub-account orders, please contact the sub-account for payment.", "more_bookings": "More Bookings", "header_things_to_do": "Things to do", "header_wifi_and_sim": "WiFi & SIM Card", "msp": "MSP", "suggest_low_price": "MSP (Minimum Selling Price) is a price we suggest you to set your selling price not lower than it.", "booking.export_date": "Select Date Range To Export", "tmall_bookings": "飞猪码商订单", "day_of_travel": "The Day", "before_travel": "{0} days", "booking.border_date": "Please select the booking date (The maximum selection date range is 6 months)", "price_from": "<span style='font-size:12px'>From</span> <b>{0}</b>", "price_currency": "Currency：", "Add_to_wish_list": "Add to Wishlist", "wish_list_added": "Wishlist", "flash_sale_within": "Flash Sale within", "available_from": "Available From", "cut_off_time": "Cut off Time", "detail": "Detail", "booking_now": "Booking Now", "location": "Location", "check_available_date": "Check Available Date", "other_dates": "Other Dates", "participation_time": "Participation Time", "buy_detail": "Detail", "activity.v2.error.package_max_pax": "You must select no more than <span class='max_pax'>{0}</span> for this package", "activity.v2.error.package_min_to_max": "You must select at least <span class='min_pax'>{0}</span> and no more than <span class='max_pax'>{1}</span> for this package", "travaler_information": "Traveler's Information", "concact_information": "Contact Information", "all_required": "Please complete all required fields to continue", "all_required_right": "Please use a valid format", "fold_up": "Fold Up", "bank_top_up_tips": "Reminder: From April 8, 2021, an application isn't needed for bank transfer top-ups. After the transfer reaches the account, it will be automatically topped up to your account balance.", "top_up_account": "KLKTECH Account Number", "hotel": {"adults_with_number": "{0} adults", "adultone_with_number": "{0} adult", "children_with_number": "{0} Children", "childone_with_number": "{0} child", "confirm_booking_info_age": "Age", "nights_with_number": "{0} nights", "night_one": "1 night", "hotel_info": "Hotel Info", "check_in_date": "Check-in date", "check_out_date": "Check-out date", "hotel_order": "Hotel Order", "room_and_quantity": "Room/Quantity", "check_in_out_date": "Check-in/out date", "guest_name": "Guest name"}, "near_by_activities": "Explore what's near", "SZOverride": "Shenzhen City, Mainland China", "hotels": "Hotels", "modify_other_info": "修改出行人信息", "modify_activity_name": "商品名称", "remain_modify_count": "剩余修改次数", "modify_history": "历史更改信息", "modify_time": "修改时间", "modify_status": "修改状态", "modify_fail_reason": "失败原因", "modify_package_name": "套餐", "modify_sku": "Number Of Purchased", "modify_tips": "修改说明", "modify_cancel": "放弃", "modify_submit": "提交修改", "modifying": "修改中", "modify_fail": "修改失败", "modify_success": "修改成功", "required_terms": "You'll need to agree to the required terms to continue", "generate_batch_order_tips": "It may take several minutes to prepare your order. Please keep this window open.", "order_generating": "Pending creating order", "agent_export_date": "Please select date range to export", "agent_export_date_tips": "The maximum selection date range is 6 months", "export_error_no_logs": "No records found within the specified time range", "export_error_over_six_months": "Please specify a time range of not more than six months", "109497": "Encrypted export", "show_all_texts": "Show all texts", "hide_partial_texts": "Hide partial texts", "history_bookings": "History Bookings", "booking_before_this_date": "Only bookings before 2024-06-12 can be viewed on this page.Please click {0} for bookings after 2024-06-12.", "booking_after_this_date": "Only bookings after 2024-06-12 can be viewed on this page.Please click {0} for bookings before 2024-06-12", "znd_auth_login": "Authorization for Login", "znd_welcome": "Welcome to the Klook Premium Hotel Distribution Platform", "znd_auth_tips_1": "klookzhotel.klktech.cn is the Klook premium hotel distribution platform, offering distribution of high-end hotel products from brands such as Mandarin Oriental, Aman, and Rosewood. Some products include competitive accommodation benefits such as \"stay four nights, pay for three,\" \"stay three nights, pay for two,\" $100 in consumption credit, and free room upgrades (subject to the details of each hotel room type). We cordially invite all agents to actively participate in distribution and share in the profits by expanding the market together!", "znd_auth_tips_2": "Please note that the distribution model for such high-end hotels differs from other Klook products:<br/>1. Agents place orders on the distribution platform with a credit card guarantee, with guests paying the room fee upon arrival at the hotel.<br/>2. Commissions are returned after the guest checks out, with Klook settling the commissions to the agents. Detailed commission ratios can be found on the hotel detail page.", "znd_auth_settlement_desc_1": "To create distribution orders and calculate commissions, please click \"Authorize Login\" to synchronize your agent account to klookzhotel.klktech.cn.", "znd_auth_settlement_desc_2": "After completing the authorization login, you will need to visit klookzhotel.klktech.cn to view and manage your distribution orders and commissions.", "znd_auth_remind": "Please note:In order to keep the login-state account in agent marketplace is connected with the account in klookzhotel.com.cn，Please Click\"One-Click Access To Luxury Hotels\" again via agent marketplace once agent marketplace account logout.", "znd_title": "One-Click Access To Luxury Hotels", "otp.phone.title": "For your account security, please verify phone number", "otp.email.title": "For your account security, please verify contact email", "otp.phone.desc": "Verification code's been sent to phone number:", "otp.email.desc": "Verification code's been sent to email:", "otp.resend": "Resend Code", "otp.resend_countdown": "Resend Code After {0}s", "otp.phone.help_tip": "Invalid Phone Number", "otp.email.help_tip": "<PERSON><PERSON><PERSON>", "otp.phone.help_desc": "Please contact our staff to modify it if phone number is invalid.", "otp.email.help_desc": "Please contact our staff to modify it if contact email is invalid.", "pay.select.session": "Select session", "split_by_product_quantity": "Get a separate voucher for each quantity ordered.", "read_before_check": "Please click and read this instruction before checking the option", "check_option_explanation": "1. Check this option : one order will be split into multiple bookings based on the quantity purchased , and each booking will contain a separate voucher. Order will be displayed in \"Voucher-Split Orders\" tab", "example_explanation": "2. for instance , order with 15 adult tickets and 10 child tickets , will be split into 25 bookings , and every booking has its own separate voucher.", "no_check_option_explanation": "3. otherwise, one order will generate one booking ,and the booking contains a voucher with all tickets.", "feature_usage": "4. This feature is usually used for pre-hoarding tickets, only open for selected pacakges. To get the full package list , please contact your sales manager.", "max_min_quantity": "5. A maximum of 50 tickets can be ordered at a time ,with a minimum of 2 tickets.", "export_tip": "If the order export fails, try to narrow the export period.", "109149": "<PERSON>pied", "fixedDateDesc": "Valid only on specified dates", "openDateDesc": "Valid for any day within the validity period"}