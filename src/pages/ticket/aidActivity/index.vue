<template>
    <div class="ticket-activity-index">
        <!--    导航-->
        <activity-nav :activity-info="activityInfo"></activity-nav>
        <!--    图片-->
        <klk-carousel>
            <klk-carousel-item
                v-for="item in bannerImages"
                :key="item.image_url"
            >
                <img
                    class="banner-img"
                    :src="item.image_url"
                    :alt="item.image_desc"
                />
            </klk-carousel-item>
        </klk-carousel>
        <!--    活动信息-->
        <activity-intro
            :activity-info="activityInfo"
            @update-activity="updateActivityInfo"
        ></activity-intro>
        <!--    套餐选择-->
        <div id="packageOptions">
            <klk-section-title
                size="h2"
                class="package-options-title"
                decorative-line
            >
                {{ $t('activity.v2.label.package_options') }}
            </klk-section-title>
            <!--   套餐详情-->
            <activity-package
                v-for="packageItem in packages"
                :key="packageItem.package_id"
                :package-info="packageItem"
                :activity-info="activityInfo"
                @toggle-sku-detail="toggleSkuDetail"
                :show-sku-details="showSkuDetails"
            >
            </activity-package>
        </div>
        <div class="activity-info-wrapper">
            <div class="left-wrapper">
                <!-- 渲染section_info -->
                <section-info-render
                    id="activitySections"
                    :section-info="activityInfo.section_info || []"
                ></section-info-render>
                <!-- itinerary -->
                <div id="itinerary" v-if="showItinerary && itineraryComponent">
                    <component
                        :is="itineraryComponent"
                        :spuId="spuId"
                        :currency="activityInfo.currency"
                    ></component>
                </div>
                <AidActivityImages
                    :images="activityInfo.images || []"
                ></AidActivityImages>
                <!-- 添加FAQ组件 -->
                <activity-faq
                    id="faq"
                    :faq="activityInfo.faq || []"
                ></activity-faq>
                <klk-section-title
                    v-if="!showItinerary"
                    id="location"
                    size="h2"
                    class="what-to-expect-title"
                    decorative-line
                >
                    {{ $t('location') }}
                </klk-section-title>
                <div class="content-activity-map" id="activityMap" v-if="!showItinerary">
                    <img
                        class="image"
                        @click="goMap"
                        :src="activityInfo.map_box_image_url"
                        alt="map"
                    />
                </div>
            </div>
            <div class="right-wrapper">
                <div class="nav-item-content">
                    <div
                        class="nav-item"
                        :class="{ active: nav.key === navActive }"
                        v-for="nav in navList"
                        :key="nav.key"
                    >
                        <span class="text" @click="goNav(nav.key)">
                            {{ nav.text }}
                        </span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import { filterBlockData } from '@/common/util';
import throttle from 'lodash/throttle';
import { elmScrollTop } from '@/pages/ticket/activity/activity';
import ActivityNav from './activity-nav.vue';
import ActivityIntro from './activity-intro.vue';
import AidActivityImages from './component/aidActivityImages.vue';
import ActivityPackage from './package/index.vue';
import SectionInfoRender from './component/section-info-render.vue';
import ActivityFaq from './component/faq.vue';

export default {
    name: 'AidActivityIndex',
    components: {
        ActivityNav,
        ActivityIntro,
        AidActivityImages,
        ActivityPackage,
        SectionInfoRender,
        ActivityFaq,
    },
    props: {
        activityInfo: {
            type: Object,
            required: true,
            default: () => ({}),
        },
    },
    data() {
        return {
            navActive: 'packageOptions',
            showSkuDetails: [],
            showSkuCalendars: [],
            itineraryComponent: null, // 用于存储动态加载的组件
        };
    },
    computed: {
        // itinerary 专用
        spuId() {
            const list = this.packages || [];
            return (
                list.find(item => item.page_type === 'SPU')?.package_id || null
            );
        },
        // 添加新的计算属性，判断是否显示行程组件
        showItinerary() {
            return (
                this.activityInfo.category_info &&
                this.activityInfo.category_info.sub_category_id === 2
            );
        },
        packages() {
            return this.activityInfo.package_list || [];
        },
        bannerImages() {
            const images = this.activityInfo.images || [];
            return images
                .filter(item => item.image_type === 'BANNER')
                .map(item => ({
                    image_url: item.image_url_host,
                    image_desc: item.image_alt,
                }));
        },
        navList() {
            const sectionInfo = (this.activityInfo.section_info || [])
                .map(section => {
                    const filteredGroups = (section.groups || []).filter(
                        group =>
                            group &&
                            (group.content ||
                                (group.group_name && group.items?.length)),
                    );

                    if (filteredGroups.length > 0) {
                        return {
                            ...section,
                            filteredGroups,
                        };
                    }

                    return null;
                })
                .filter(section => section !== null)
                .map(item => ({
                    key: item.section_name,
                    text: item.section_name,
                }));

            const baseNavList = [
                {
                    key: 'packageOptions',
                    text: this.$t('activity.v2.label.package_options'),
                },
            ].concat(sectionInfo);

            // 如果是旅游类别且有行程信息，添加行程导航项
            if (this.showItinerary) {
                baseNavList.push({
                    key: 'itinerary',
                    text: this.$t('itinerary'),
                });
            }

            // 只有当FAQ有数据时才添加FAQ导航项
            const hasFaqData =
                this.activityInfo.faq && this.activityInfo.faq.length > 0;
            if (hasFaqData) {
                baseNavList.push({
                    key: 'faq',
                    text: this.$t('FAQ'),
                });
            }

            if (!this.showItinerary) {
                baseNavList.push({
                    key: 'location',
                    text: this.$t('location'),
                });
            }

            return baseNavList;
        },
    },
    methods: {
        filterBlockData,
        toggleSkuDetail(id) {
            if (this.showSkuDetails.includes(id)) {
                this.showSkuDetails.splice(
                    this.showSkuDetails.findIndex(item => item === id),
                    1,
                );
            } else {
                this.showSkuDetails.push(id);
            }
        },
        navActiveByScroll() {
            return throttle(() => {
                const navList = [...this.navList].reverse();
                let foundActive = false;

                // 检查元素是否在视口内或已经滚动过
                navList.forEach(item => {
                    const el = document.getElementById(item.key);
                    if (!el) return;

                    const rect = el.getBoundingClientRect();
                    const isVisible =
                        rect.top < window.innerHeight * 0.7 && rect.bottom > 0;

                    if (isVisible && !foundActive) {
                        this.navActive = item.key;
                        foundActive = true;
                    }
                });

                // 特殊处理最后一个元素（location）
                if (!foundActive && navList.length > 0) {
                    const lastEl = document.getElementById(navList[0].key); // 因为是reverse过的，所以第一个是最后的元素
                    if (
                        lastEl &&
                        window.innerHeight + window.pageYOffset >=
                            document.body.offsetHeight - 100
                    ) {
                        this.navActive = navList[0].key;
                    }
                }
            }, 100)();
        },
        goNav(nav) {
            elmScrollTop(nav);
        },
        goMap() {
            const address = this.activityInfo.location.split(',');
            window.open(
                `https://maps.google.com/maps?daddr=${address[0]},${address[1]}&amp;ll=?&klookNewPage=true`,
            );
        },
        updateActivityInfo() {},
        // 加载行程组件
        async loadItineraryComponent() {
            if (this.showItinerary && !this.itineraryComponent) {
                try {
                    console.log('开始加载行程组件...');
                    // 动态导入组件
                    const module = await import('./component/itinerary.vue');
                    this.itineraryComponent = module.default;
                    console.log('行程组件加载成功:', this.itineraryComponent);
                } catch (error) {
                    console.error('加载行程组件失败:', error);
                    // 可以设置一个错误状态或者显示错误信息
                    this.itineraryComponent = null;
                }
            }
        },
    },
    mounted() {
        this.$store.commit('UPDATE_SHOPPING_CART_STATUS', false);
        window.addEventListener('scroll', this.navActiveByScroll);
        // 加载行程组件
        this.loadItineraryComponent();
    },
    beforeDestroy() {
        this.$store.commit('UPDATE_SHOPPING_CART_STATUS', true);
        window.removeEventListener('scroll', this.navActiveByScroll);
    },
    watch: {
        // 监听showItinerary变化，动态加载组件
        showItinerary(newVal) {
            if (newVal) {
                this.loadItineraryComponent();
            }
        },
    },
};
</script>

<style lang="scss">
.ticket-activity-index {
    .gray {
        color: #888888;
    }

    width: 1172px;
    margin: 0 auto;

    .klk-carousel {
        width: 1120px;
        height: 400px;

        .banner-img {
            height: 100%;
            border-radius: 4px;
        }
    }

    .package-options-title {
        margin-bottom: 20px;
    }

    .activity-info-wrapper {
        position: relative;
        display: flex;
        justify-content: space-between;

        .left-wrapper {
            flex: 0 0 760px;
        }

        .right-wrapper {
            flex: 1 1 auto;
            position: sticky;
            top: 20px;

            .nav-item-content {
                position: sticky;
                top: 60px;

                .nav-item {
                    margin-top: 20px;
                    float: right;
                    width: 280px;

                    font-size: 16px;
                    font-weight: bold;
                    position: relative;

                    &::before {
                        display: inline-block;
                        content: '';
                        position: absolute;
                        left: 0;
                        top: 0;
                        width: 4px;
                        border-radius: 2px;
                        height: 16px;
                        margin-top: 3px;
                    }

                    &.active {
                        color: #4c87e6;

                        &::before {
                            background-color: #4c87e6;
                        }
                    }

                    .text {
                        cursor: pointer;
                        margin-left: 16px;
                    }
                }
            }
        }
    }
}
</style>
