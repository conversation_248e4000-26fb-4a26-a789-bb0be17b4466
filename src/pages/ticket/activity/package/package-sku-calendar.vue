<template>
    <klk-poptip
        class="package-sku-calendar"
        :arrow="false"
        placement="bottom"
        :max-height="600"
        :offset="[10, 20]"
        trigger="none"
        v-model="visible"
        v-clickoutside="hideCalendar.bind(this)"
    >
        <klk-date-picker
            v-loading="loading"
            slot="content"
            @select="onChange"
            :view-switchable="false"
            :min-date="minDate"
            :date.sync="selectedDate"
            shadow
            :should-disable-date="disabledDate"
        >
            <template
                slot="date-append"
                slot-scope="{ date, disabled, selected }"
            >
                <div class="date-cell-content">
                    {{ getPrice(date, disabled) }}
                    <div v-if="isConfirmed(date)" class="day-dot"></div>
                </div>
            </template>
            <template slot="tip">
                <div class="calendar-tip">
                    <span>
                        <klk-icon type="icon_tips_tips" size="14"></klk-icon>
                        {{ `${$t('price_currency')} ${currencySymbol}` }}
                    </span>
                    <span class="confirmed-legend" v-if="departure">
                        <span class="dot-tips"></span>
                        {{ $t('confirmed_departure') }}
                    </span>
                </div>
            </template>
        </klk-date-picker>
        <div @click="visible = !visible" class="calendar-wrapper">
            <slot name="prepend"></slot>
            <img
                class="calendar"
                src="../../../../assets/imgs/calendar.png"
                alt=""
            />
        </div>
    </klk-poptip>
</template>

<script>
import { format } from 'date-fns';
import clickoutside from '@/directives/clickoutside';
import urlObj from '@/common/url';
import { mapState } from 'vuex';

export default {
    name: 'TicketActivityPackageCalendar',
    directives: {
        clickoutside,
    },
    props: {
        departure: {
            type: Boolean,
            default: false,
        },
        activityInfo: {
            type: Object,
            required: true,
            default: () => {},
        },
        packageId: {
            type: String | Number,
            required: true,
            default: '',
        },
        skuId: {
            type: String | Number,
            required: true,
            default: '',
        },
        resetDate: {
            type: Boolean,
            default: false,
        },
    },
    data() {
        return {
            visible: false,
            loading: false,
            selectedDate: null,
            schedules: [],
            currencySymbol: '',
        };
    },
    watch: {
        visible(newVal) {
            if (newVal) {
                this.schedules = [];
                this.loading = true;
                klook.ajaxGet(
                    urlObj.ticket.activity.sku_schedules(
                        this.packageId,
                        this.skuId,
                    ),
                    {},
                    res => {
                        if (res.success && res.result) {
                            this.schedules = res.result.schedules || [];
                            this.currencySymbol = res.result.currency;
                            this.loading = false;
                        }
                    },
                );
            }
        },
    },
    computed: {
        ...mapState('ticket/pay', ['packageSchedules']),
        minDate() {
            return new Date('1900-01-01');
        },
        dateFmt() {
            return 'YYYY-MM-DD';
        },
    },
    methods: {
        onChange(date) {
            const formatDate = format(date, this.dateFmt);
            this.visible = false;
            this.loading = true;
            this.$emit('change-date', {
                date: formatDate,
                packageId: this.packageId,
                skuId: this.skuId,
            });
            // 重置日期
            this.$nextTick(() => {
                this.onResetDate();
            });
        },
        onResetDate() {
            this.selectedDate = this.resetDate ? null : this.selectedDate;
        },
        hideCalendar() {
            this.visible = false;
        },
        disabledDate(date) {
            const index = this.schedules.findIndex(
                item => item.date === format(date, this.dateFmt),
            );
            return index === -1 || this.schedules[index].stock <= 0;
        },
        getPrice(date, disabled) {
            if (disabled) return '';
            return this.formatPriceThousands(
                (
                    this.schedules.find(
                        item => item.date === format(date, this.dateFmt),
                    ) || {}
                ).selling_price,
            );
        },
        isConfirmed(date) {
            const day = format(date, this.dateFmt);
            const schedule = this.schedules.find(item => item.date === day);
            return schedule && schedule.guarantee_group;
        },
    },
};
</script>

<style lang="scss">
.package-sku-calendar {
    .klk-poptip-popper-inner {
        margin: 0;
        padding: 0;
    }

    .calendar-wrapper {
        display: flex;
        align-items: center;
    }

    .date-cell-content {
        display: flex;
        align-items: center;
        justify-content: center;
        flex-direction: column;
    }

    .dot-tips,
    .day-dot {
        display: inline-block;
        width: 6px;
        height: 6px;
        background-color: green;
        border-radius: 50%;
        margin-right: 4px;
    }

    .calendar-tip {
        display: flex;
        justify-content: space-between;
        align-items: center;
        width: 100%;
    }

    .confirmed-legend {
        display: flex;
        align-items: center;
    }
}
</style>
