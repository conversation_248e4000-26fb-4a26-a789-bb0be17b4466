<template>
    <div class="pay-package-skus date-time-wrapper">
        <p class="title">{{ $t('quantity') }}</p>
        <p class="tips" v-if="packageTips" v-html="packageTips"></p>
        <div
            class="package-wrapper"
            v-for="(sku, index) in skuPrices"
            :key="sku.sku_id"
        >
            <div
                class="package-content"
                :class="{ selected: +sku.sku_id === +skuId }"
            >
                <div class="sku-name">
                    {{ sku.name }}
                    <p class="must-buy" v-if="sku.required">
                        {{ $t('must_buy') }}
                    </p>
                </div>

                <p class="sku-detail" @click="showSku(sku.sku_id)">
                    {{ $t('buy_detail') }}
                    <klk-icon
                        class="detail-icon"
                        :class="{ up: showDetailSku === sku.sku_id }"
                        size="10"
                        type="detail"
                    ></klk-icon>
                </p>
                <div class="sku-price">
                    {{ getCurrencySymbolByCurrencyCode(sku.currency) }}
                    {{ formatPriceThousands(sku.price) }}
                    <del class="gray" v-if="+sku.market_price > +sku.price">
                        {{ formatPriceThousands(sku.market_price) }}
                    </del>
                </div>
                <div
                    class="sku-sold-out"
                    v-if="sku.is_price_overflow || sku.stock <= 0"
                >
                    {{ $t('activity.v2.btn.sold_out') }}
                </div>
                <div class="sku-operate" v-else>
                    <div
                        class="reduce opt left"
                        @click="deleteQuality(index, sku)"
                    >
                        <klk-icon
                            :color="
                                selectedQuality[index] === 0
                                    ? '#CCCCCC'
                                    : '#4D87E5'
                            "
                            type="icon_other_minus_xs"
                        ></klk-icon>
                    </div>
                    <klk-input
                        size="small"
                        class="count"
                        type="number"
                        name="skuQualityInput"
                        ref="counter"
                        v-model="selectedQualityCopy[index]"
                        onKeypress="return (/[\d]/.test(String.fromCharCode(event.keyCode)))"
                        @input="
                            num => {
                                handleQuality(num, index, sku);
                            }
                        "
                        :min="0"
                    ></klk-input>
                    <div
                        class="increase opt right"
                        @click="addQuality(index, sku)"
                    >
                        <klk-icon
                            :color="
                                selectedQuality[index] >= sku.max_pax
                                    ? '#CCCCCC'
                                    : '#4D87E5'
                            "
                            type="icon_other_plus_xs"
                        ></klk-icon>
                    </div>
                </div>
            </div>
        </div>

        <klk-modal
            class="package-sku-detail-modal"
            v-if="packageInfo.package_id"
            size="large"
            :open="showDetailSku !== ''"
            :closable="true"
            :scrollable="true"
            @close="showDetailSku = ''"
            @on-close="showDetailSku = ''"
            :show-default-footer="false"
        >
            <klk-section-title size="h4" slot="header">
                {{ $t('activity.v2.label_more_info') }}
            </klk-section-title>
            <!--          套餐详情-->
            <div style="height: calc(100vh - 300px);">
                <package-sku-detail
                    :package-detail="packageInfo"
                ></package-sku-detail>
            </div>
        </klk-modal>
    </div>
</template>

<script>
import { mapState } from 'vuex';
import urlObj from '@/common/url';
import debounce from 'lodash/debounce';
import PackageSkuDetail from '../activity/package/package-sku-detail.vue';

export default {
    components: {
        PackageSkuDetail,
    },
    props: {
        activityInfo: {
            type: Object,
            required: true,
            default: () => {},
        },
        minPax: { type: Number, default: 0, required: true },
        maxPax: { type: Number, default: 9999, required: true },
        isAid: {
            type: Boolean,
            required: true,
            default: false,
        },
    },
    data() {
        return {
            showDetailSku: '',
            packageInfo: {},
            selectedQualityCopy: [],
        };
    },
    computed: {
        ...mapState('ticket/pay', [
            'packageId',
            'skuId',
            'skuPrices',
            'selectedQuality',
        ]),
        qualitySum() {
            return this.getSum(this.selectedQuality);
        },
        packageTips() {
            // 最小预订数量大于等于2，且最大预订数量为unlimited时
            if (this.minPax > 1 && this.maxPax === 9999) {
                return this.$t('activity.v2.error.package_min_pax', [
                    this.minPax,
                ]);
            }

            // 最小预订数量为1，且最大预订数量为非unlimited时
            if (this.minPax === 1 && this.maxPax < 9999) {
                return this.$t('activity.v2.error.package_max_pax', [
                    this.maxPax,
                ]);
            }

            // 最小预订数量大于等于2，且最大预订数量为非unlimited时
            if (this.minPax > 1 && this.maxPax < 9999) {
                return this.$t('activity.v2.error.package_min_to_max', [
                    this.minPax,
                    this.maxPax,
                ]);
            }
        },
    },
    watch: {
        skuPrices: {
            handler: function(newVal = []) {
                if (newVal && newVal.length === 0) return;
                const skuInfo =
                    newVal.find(item => +item.sku_id === +this.skuId) || {};
                if (skuInfo.is_price_overflow || skuInfo.stock <= 0) {
                    this.$alert(
                        `${skuInfo.name}${this.$t('activity.v2.btn.sold_out')}`,
                    ).then(() => {
                        window.history.back();
                    });
                } else {
                    const index = newVal.findIndex(
                        val => +val.sku_id === +this.skuId,
                    );
                    const selectedQuality = newVal.map((item, itemIndex) => {
                        return item.required || index === itemIndex
                            ? +item.min_pax
                            : 0;
                    });

                    const sum = selectedQuality.reduce(
                        (acc, cur) => acc + cur,
                        0,
                    );

                    if (sum < this.minPax) {
                        selectedQuality[index] =
                            this.minPax - (sum - selectedQuality[index]);
                    }
                    this.$store.commit(
                        'ticket/pay/UPDATE_SELECTED_QUALITY',
                        selectedQuality,
                    );
                }
            },
            immediate: true,
        },
        selectedQuality: {
            handler: function(newVal = []) {
                this.selectedQualityCopy = [...newVal];
            },
            immediate: true,
        },
    },
    methods: {
        fetchDetail() {
            const url = this.isAid
                ? urlObj.ticket.pay.package_aidSku_detail
                : urlObj.ticket.pay.package_sku_detail(
                      this.packageId,
                      this.skuId,
                  );

            const payload = this.isAid
                ? {
                      package_id: this.packageInfo.package_id,
                  }
                : {};
            klook.ajaxGet(url, payload, res => {
                if (res.success && res.result) {
                    this.packageInfo = res.result;
                }
            });
        },

        showSku(id) {
            this.showDetailSku = id;
        },
        isPriceOverflow(sku) {
            return sku.is_price_overflow;
        },
        getSum(qualities) {
            return qualities.reduce((acc, cur) => acc + cur, 0);
        },
        handleDecrease(num, index, sku) {
            const selectedQuality = [].concat(this.selectedQuality); // deep copy
            let quality = num;
            // sku 最小购买数量
            if (Math.max(1, sku.min_pax) > quality) {
                if (sku.required || +this.skuId === +sku.sku_id) {
                    this.$toast(
                        this.$t('activity.error.price_min', [
                            Math.max(1, sku.min_pax),
                        ]),
                    );
                    this.$set(
                        this.selectedQualityCopy,
                        index,
                        this.selectedQuality[index],
                    );
                    return;
                } else {
                    quality = 0;
                    this.$set(this.selectedQualityCopy, index, quality);
                }
            }
            selectedQuality[index] = quality;
            //  package 最小购买数量
            if (this.getSum(selectedQuality) < this.minPax) {
                this.$toast(
                    this.$t('activity.error.package_min', [this.minPax]),
                );
                this.$set(
                    this.selectedQualityCopy,
                    index,
                    this.selectedQuality[index],
                );
                return;
            }

            this.$store.commit(
                'ticket/pay/UPDATE_SELECTED_QUALITY',
                selectedQuality,
            );
        },
        handleIncrease(num, index, sku) {
            const selectedQuality = [].concat(this.selectedQuality);
            let quality = Math.max(num, sku.min_pax);
            // sku 最大购买数量
            if (quality > sku.max_pax) {
                this.$toast(
                    this.$t('activity.error.price_max_v2', [sku.max_pax]),
                );
                this.$set(
                    this.selectedQualityCopy,
                    index,
                    this.selectedQuality[index],
                );
                return;
            }
            selectedQuality[index] = quality;

            //  package 最大购买数量
            if (this.getSum(selectedQuality) > this.maxPax) {
                this.$toast(
                    this.$t('activity.error.package_max', [this.maxPax]),
                );
                this.$set(
                    this.selectedQualityCopy,
                    index,
                    this.selectedQuality[index],
                );
                return;
            }
            this.$store.commit(
                'ticket/pay/UPDATE_SELECTED_QUALITY',
                selectedQuality,
            );
        },
        deleteQuality(index, sku) {
            if (this.isPriceOverflow(sku)) {
                return false;
            }
            this.handleDecrease(this.selectedQuality[index] - 1, index, sku);
        },
        addQuality(index, sku) {
            if (this.isPriceOverflow(sku)) {
                return false;
            }
            this.handleIncrease(this.selectedQuality[index] + 1, index, sku);
        },
        handleQuality(num, index, sku) {
            if (this.isPriceOverflow(sku)) {
                return false;
            }
            // 当输入为''的时候，赋予原始的默认值
            if (+num === this.selectedQuality[index]) {
                this.$set(
                    this.selectedQualityCopy,
                    index,
                    this.selectedQuality[index],
                );
                return false;
            }
            if (+num < this.selectedQuality[index]) {
                this.handleDecrease(+num, index, sku);
            }
            if (+num > this.selectedQuality[index]) {
                this.handleIncrease(+num, index, sku);
            }
        },
    },
    mounted() {
        this.fetchDetail();
        this.handleQuality = debounce(this.handleQuality, 500);
    },
};
</script>

<style lang="scss">
.pay-package-skus {
    .tips {
        line-height: 20px;
        margin-bottom: 8px;
        font-size: 12px;

        .min_pax,
        .max_pax {
            color: #ff5722;
        }
    }

    .package-wrapper {
        .package-content {
            min-height: 48px;
            background: #ffffff;
            border: 1px solid #f5f5f5;
            border-radius: 2px;
            display: flex;
            align-items: center;
            padding: 0 18px;
            margin-bottom: 8px;

            &.selected {
                background: #f5f5f5;
            }

            .sku-name {
                flex: 0 0 200px;
                padding: 10px 20px 10px 0;
                font-size: 16px;
                font-weight: 600;

                .must-buy {
                    margin-top: 4px;
                    line-height: 15px;
                    font-size: 12px;
                    color: #ffa628;
                    font-weight: 400;
                }
            }

            .sku-detail {
                flex: 0 0 80px;
                color: #4d87e5;
                cursor: pointer;

                .detail-icon {
                    margin-left: 4px;
                    transform: rotate(90deg);

                    &.up {
                        transform: rotate(-90deg);
                    }
                }
            }

            .sku-price {
                flex: 0 0 280px;
                padding-left: 100px;
                font-weight: 700;
                font-size: 18px;

                del {
                    color: #888888;
                    font-weight: 400;
                    font-size: 14px;
                    margin-left: 8px;
                }
            }

            .sku-operate,
            .sku-sold-out {
                flex: 1 1 auto;
                display: flex;
                justify-content: flex-end;

                .count {
                    width: 60px;
                    border-top: 1px solid #f5f5f5;
                    border-bottom: 1px solid #f5f5f5;

                    .klk-input-inner {
                        border: none;
                        background: none;

                        input {
                            padding: 0;
                            text-align: center;
                        }

                        input::-webkit-outer-spin-button,
                        input::-webkit-inner-spin-button {
                            -webkit-appearance: none;
                        }
                        input[type='number'] {
                            -moz-appearance: textfield;
                        }
                    }
                }

                .opt {
                    width: 36px;
                    line-height: 36px;
                    cursor: pointer;
                    background: #fff;
                    text-align: center;
                    border: 1px solid #f5f5f5;

                    &.left {
                        border-top-left-radius: 4px;
                        border-bottom-left-radius: 4px;
                    }
                    &.right {
                        border-top-right-radius: 4px;
                        border-bottom-right-radius: 4px;
                    }
                }
            }

            &.selected {
                background: #f5f5f5;
                .count {
                    background-color: #fff;
                }
            }
            // .sku-operate{
            //     .klk-counter-decrease,
            //     .klk-counter-increase
            //     {
            //         background-color: #fff;
            //         .klk-icon{
            //             color: #4d87e5;
            //         }
            //         .klk-counter-input input{
            //             width: 45px;
            //         }
            //     }
            // }
        }
    }
}
</style>
