<template>
    <div>
        <div class="confirmed-legend-wrapper" v-if="showConfirmedLegend">
            <span class="confirmed-dot"></span>
            <span>{{ $t('confirmed_departure') }}</span>
        </div>
        <div class="ticket-pay-package-date date-time-wrapper">
            <p class="title">{{ $t('check_available_date') }}</p>
            <div
                class="date-time-content date-content"
                :class="{
                    disabled: typeof getPriceByDate(date) === 'undefined',
                    active: date === selectedDate,
                }"
                v-for="date in showDays"
                :key="date"
                @click="updateSelectedDate(date)"
            >
                <p class="date">
                    {{ getDateText(date) }}
                    <span v-if="isConfirmed(date)" class="confirmed-dot"></span>
                </p>
                <p
                    class="price"
                    v-if="typeof getPriceByDate(date) !== 'undefined'"
                >
                    {{ getCurrencySymbolByCurrencyCode(currencySymbol) }}
                    {{ getPriceByDate(date) }}
                </p>
            </div>
            <div
                class="date-time-content date-content other-date"
                :class="{ active: !showDays.includes(selectedDate) }"
            >
                <!--    价格日历-->
                <package-sku-calendar
                    :activity-info="activityInfo"
                    :package-id="packageId"
                    :sku-id="skuId"
                    :departure="showConfirmedLegend"
                    @change-date="changeDate"
                >
                    <div slot="prepend">
                        <p class="date" v-if="showDays.includes(selectedDate)">
                            {{ $t('other_dates') }}
                        </p>
                        <template v-else>
                            <p class="date">
                                <span
                                    v-if="isConfirmed(selectedDate)"
                                    class="confirmed-dot"
                                ></span>
                                {{ selectedDate }}
                            </p>
                            <p class="price">
                                {{ currencySymbol }}
                                {{ getPriceByDate(selectedDate) }}
                            </p>
                        </template>
                    </div>
                </package-sku-calendar>
            </div>
        </div>
    </div>
</template>

<script>
import { mapState } from 'vuex';
import { addDays, isBefore, format, isToday, isTomorrow } from 'date-fns';
import urlObj from '@/common/url';
import PackageSkuCalendar from '../activity/package/package-sku-calendar.vue';

export default {
    components: {
        PackageSkuCalendar,
    },
    props: {
        activityInfo: {
            type: Object,
            required: true,
            default: () => {},
        },
    },
    data() {
        return {
            currencySymbol: '',
            skuSchedules: [],
            showDays: [],
            showConfirmedLegend: false,
        };
    },
    computed: {
        ...mapState('ticket/pay', [
            'packageId',
            'skuId',
            'selectedDate',
            'packageSchedules',
        ]),
    },
    methods: {
        getDateText(date) {
            if (isToday(date)) return this.$t('today');
            if (isTomorrow(date)) return this.$t('search_date_tomorrow');
            return date;
        },
        isConfirmed(date) {
            const schedule = this.packageSchedules[date];
            return (
                schedule && (schedule || []).some(item => item.guarantee_group)
            );
        },
        changeDate(data) {
            this.$store.commit('ticket/pay/UPDATE_SELECTED_DATE', data.date);
        },
        getPriceByDate(date) {
            const index = this.skuSchedules.findIndex(
                item => item.date === date,
            );

            if (index === -1 || this.skuSchedules[index].stock <= 0)
                return undefined;

            return this.formatPriceThousands(
                this.skuSchedules[index].selling_price,
            );
        },
        fetchSkuPrice() {
            klook.ajaxGet(
                urlObj.ticket.activity.sku_schedules(
                    this.packageId,
                    this.skuId,
                ),
                {},
                res => {
                    if (res.success && res.result) {
                        this.skuSchedules = res.result.schedules || [];
                        const availableDate = format(
                            res.result.start_time,
                            'YYYY-MM-DD',
                        ); //  最早可预定日期
                        if (!this.selectedDate) {
                            //  如果进来时没有选中时间，就用最早可预定日期
                            this.$store.commit(
                                'ticket/pay/UPDATE_SELECTED_DATE',
                                availableDate,
                            );
                        }
                        this.currencySymbol = res.result.currency;
                        let firstDay;
                        if (isBefore(availableDate, addDays(new Date(), 7))) {
                            // 最早可预定日期在一周内
                            firstDay = new Date();
                        } else {
                            firstDay = availableDate;
                        }
                        this.showConfirmedLegend = this.skuSchedules.some(
                            item => item.guarantee_group,
                        );
                        let i = 0;
                        while (i < 7) {
                            this.showDays.push(
                                format(addDays(firstDay, i++), 'YYYY-MM-DD'),
                            );
                        }
                    }
                },
            );
        },
        updateSelectedDate(date) {
            if (typeof this.getPriceByDate(date) === 'undefined') return;
            this.$store.commit('ticket/pay/UPDATE_SELECTED_DATE', date);
        },
    },
    created() {
        // sku 价格日历
        this.fetchSkuPrice();
    },
};
</script>

<style lang="scss">
.ticket-pay-package-date {
    .date-time-content {
        position: relative;
    }
    .confirmed-dot {
        position: absolute;
        bottom: 0;
        left: 50%;
        display: inline-block;
        width: 6px;
        height: 6px;
        background-color: green;
        border-radius: 50%;
        margin-right: 4px;
        vertical-align: middle;
    }
    .confirmed-legend-wrapper {
        display: flex;
        align-items: center;
        margin: 8px 0;
        font-size: 12px;
        color: #666;
        .confirmed-dot {
            margin-bottom: 1px;
        }
    }
}
</style>
